using System;
using System.Drawing;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    /// <summary>
    /// Calculator Pro - <PERSON><PERSON><PERSON> t<PERSON>h với giao diện đẹp, hỗ trợ số thập phân và lịch sử tính toán
    /// </summary>
    public partial class Form1 : Form
    {
        private readonly CalculatorEngine _calculator;
        private readonly CalculationHistory _history;
        private string _storedValue = "0"; // Giá trị AS

        public Form1()
        {
            InitializeComponent();
            _calculator = new CalculatorEngine();
            _history = new CalculationHistory();

            InitializeCalculator();
        }

        /// <summary>
        /// Khởi tạo calculator và thiết lập giao diện
        /// </summary>
        private void InitializeCalculator()
        {
            // Thiết lập màu nền gradient cho form
            this.Paint += Form1_Paint;

            // Cập nhật lịch sử ban đầu
            UpdateHistoryDisplay();

            // Thiết lập focus cho form
            this.KeyPreview = true;
            this.KeyDown += Form1_KeyDown;

            // Thiết lập tooltip cho các nút
            SetupTooltips();
        }

        /// <summary>
        /// Vẽ background gradient cho form
        /// </summary>
        private void Form1_Paint(object sender, PaintEventArgs e)
        {
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                this.ClientRectangle,
                Color.FromArgb(30, 30, 30),
                Color.FromArgb(50, 50, 50),
                System.Drawing.Drawing2D.LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, this.ClientRectangle);
            }
        }

        /// <summary>
        /// Thiết lập tooltip cho các nút
        /// </summary>
        private void SetupTooltips()
        {
            var tooltip = new ToolTip();
            tooltip.SetToolTip(btnAns, "Sử dụng kết quả cuối cùng (ANS)");
            tooltip.SetToolTip(btnAS, "Lưu giá trị hiện tại (AS - Answer Store)");
            tooltip.SetToolTip(btnClear, "Xóa tất cả (C)");
            tooltip.SetToolTip(btnClearEntry, "Xóa số hiện tại (CE)");
            tooltip.SetToolTip(btnBackspace, "Xóa ký tự cuối (Backspace)");
        }