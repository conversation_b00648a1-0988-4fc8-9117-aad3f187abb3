using System;
using System.Drawing;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    /// <summary>
    /// Calculator Pro - <PERSON><PERSON><PERSON> t<PERSON>h với giao diện đẹp, hỗ trợ số thập phân và lịch sử tính toán
    /// </summary>
    public partial class Form1 : Form
    {
        private readonly CalculatorEngine _calculator;
        private readonly CalculationHistory _history;
        private string _storedValue = "0"; // Giá trị AS

        public Form1()
        {
            InitializeComponent();
            _calculator = new CalculatorEngine();
            _history = new CalculationHistory();
            
            InitializeCalculator();
        }

        /// <summary>
        /// Khởi tạo calculator và thiết lập giao diện
        /// </summary>
        private void InitializeCalculator()
        {
            // Thiết lập màu nền gradient cho form
            this.Paint += Form1_Paint;
            
            // Cập nhật lịch sử ban đầu
            UpdateHistoryDisplay();
            
            // Thiết lập focus cho form
            this.KeyPreview = true;
            this.KeyDown += Form1_KeyDown;
            
            // Thiết lập tooltip cho các nút
            SetupTooltips();
        }

        /// <summary>
        /// Vẽ background gradient cho form
        /// </summary>
        private void Form1_Paint(object sender, PaintEventArgs e)
        {
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                this.ClientRectangle,
                Color.FromArgb(30, 30, 30),
                Color.FromArgb(50, 50, 50),
                System.Drawing.Drawing2D.LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, this.ClientRectangle);
            }
        }

        /// <summary>
        /// Thiết lập tooltip cho các nút
        /// </summary>
        private void SetupTooltips()
        {
            var tooltip = new ToolTip();
            tooltip.SetToolTip(btnAns, "Sử dụng kết quả cuối cùng (ANS)");
            tooltip.SetToolTip(btnAS, "Lưu giá trị hiện tại (AS - Answer Store)");
            tooltip.SetToolTip(btnClear, "Xóa tất cả (C)");
            tooltip.SetToolTip(btnClearEntry, "Xóa số hiện tại (CE)");
            tooltip.SetToolTip(btnBackspace, "Xóa ký tự cuối (Backspace)");
        }

        /// <summary>
        /// Xử lý phím tắt
        /// </summary>
        private void Form1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.D0:
                case Keys.NumPad0:
                    btn0.PerformClick();
                    break;
                case Keys.D1:
                case Keys.NumPad1:
                    btn1.PerformClick();
                    break;
                case Keys.D2:
                case Keys.NumPad2:
                    btn2.PerformClick();
                    break;
                case Keys.D3:
                case Keys.NumPad3:
                    btn3.PerformClick();
                    break;
                case Keys.D4:
                case Keys.NumPad4:
                    btn4.PerformClick();
                    break;
                case Keys.D5:
                case Keys.NumPad5:
                    btn5.PerformClick();
                    break;
                case Keys.D6:
                case Keys.NumPad6:
                    btn6.PerformClick();
                    break;
                case Keys.D7:
                case Keys.NumPad7:
                    btn7.PerformClick();
                    break;
                case Keys.D8:
                case Keys.NumPad8:
                    btn8.PerformClick();
                    break;
                case Keys.D9:
                case Keys.NumPad9:
                    btn9.PerformClick();
                    break;
                case Keys.Add:
                    btnAdd.PerformClick();
                    break;
                case Keys.Subtract:
                    btnSubtract.PerformClick();
                    break;
                case Keys.Multiply:
                    btnMultiply.PerformClick();
                    break;
                case Keys.Divide:
                    btnDivide.PerformClick();
                    break;
                case Keys.Enter:
                case Keys.Oemplus when e.Shift: // Dấu = khi nhấn Shift + =
                    btnEquals.PerformClick();
                    break;
                case Keys.OemPeriod:
                case Keys.Decimal:
                    btnDecimal.PerformClick();
                    break;
                case Keys.Back:
                    btnBackspace.PerformClick();
                    break;
                case Keys.Escape:
                    btnClear.PerformClick();
                    break;
                case Keys.Delete:
                    btnClearEntry.PerformClick();
                    break;
            }
        }

        /// <summary>
        /// Xử lý click nút số
        /// </summary>
        private void btnNumber_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button == null) return;

            string number = button.Text;
            string newDisplay = _calculator.AddNumber(number, txtDisplay.Text);
            txtDisplay.Text = newDisplay;
        }

        /// <summary>
        /// Xử lý click nút dấu thập phân
        /// </summary>
        private void btnDecimal_Click(object sender, EventArgs e)
        {
            string newDisplay = _calculator.AddDecimal(txtDisplay.Text);
            txtDisplay.Text = newDisplay;
        }

        /// <summary>
        /// Xử lý click nút phép toán
        /// </summary>
        private void btnOperator_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button == null) return;

            string operatorSymbol = button.Text;
            var result = _calculator.ProcessOperator(operatorSymbol, txtDisplay.Text);
            
            if (result.IsError)
            {
                txtDisplay.Text = result.Display;
                return;
            }

            txtDisplay.Text = result.Display;
        }

        /// <summary>
        /// Xử lý click nút bằng
        /// </summary>
        private void btnEquals_Click(object sender, EventArgs e)
        {
            var result = _calculator.Calculate(txtDisplay.Text);
            
            if (result.IsError)
            {
                txtDisplay.Text = result.Display;
                return;
            }

            txtDisplay.Text = result.Display;
            
            // Thêm vào lịch sử nếu có biểu thức
            if (!string.IsNullOrEmpty(result.Expression))
            {
                _history.AddCalculation(result.Expression, result.Display);
                UpdateHistoryDisplay();
            }
        }

        /// <summary>
        /// Xử lý click nút Clear (C)
        /// </summary>
        private void btnClear_Click(object sender, EventArgs e)
        {
            _calculator.Reset();
            txtDisplay.Text = "0";
        }

        /// <summary>
        /// Xử lý click nút Clear Entry (CE)
        /// </summary>
        private void btnClearEntry_Click(object sender, EventArgs e)
        {
            txtDisplay.Text = "0";
        }

        /// <summary>
        /// Xử lý click nút Backspace
        /// </summary>
        private void btnBackspace_Click(object sender, EventArgs e)
        {
            string newDisplay = _calculator.Backspace(txtDisplay.Text);
            txtDisplay.Text = newDisplay;
        }

        /// <summary>
        /// Xử lý click nút ANS
        /// </summary>
        private void btnAns_Click(object sender, EventArgs e)
        {
            string lastAnswer = _calculator.GetLastAnswer();
            txtDisplay.Text = lastAnswer;
        }

        /// <summary>
        /// Xử lý click nút AS (Answer Store)
        /// </summary>
        private void btnAS_Click(object sender, EventArgs e)
        {
            _calculator.StoreValue(txtDisplay.Text);
            _storedValue = txtDisplay.Text;
            
            // Hiển thị thông báo ngắn
            ShowTemporaryMessage("Đã lưu giá trị!");
        }

        /// <summary>
        /// Cập nhật hiển thị lịch sử
        /// </summary>
        private void UpdateHistoryDisplay()
        {
            txtHistory.Text = _history.GetHistoryText();
            
            // Cuộn xuống cuối để hiển thị lịch sử mới nhất
            txtHistory.SelectionStart = 0;
            txtHistory.ScrollToCaret();
        }

        /// <summary>
        /// Hiển thị thông báo tạm thời
        /// </summary>
        private void ShowTemporaryMessage(string message)
        {
            var originalText = txtDisplay.Text;
            txtDisplay.Text = message;
            
            var timer = new Timer();
            timer.Interval = 1000; // 1 giây
            timer.Tick += (s, e) =>
            {
                txtDisplay.Text = originalText;
                timer.Stop();
                timer.Dispose();
            };
            timer.Start();
        }
    }
}
