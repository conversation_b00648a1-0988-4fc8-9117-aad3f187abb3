namespace WindowsFormsApp1
{
    partial class Form1
    {
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.TextBox txtDisplay;
        private System.Windows.Forms.RichTextBox txtHistory;
        private System.Windows.Forms.Button btnClear, btnClearEntry, btnBackspace, btnDivide;
        private System.Windows.Forms.Button btn7, btn8, btn9, btnMultiply;
        private System.Windows.Forms.Button btn4, btn5, btn6, btnSubtract;
        private System.Windows.Forms.Button btn1, btn2, btn3, btnAdd;
        private System.Windows.Forms.Button btn0, btnDecimal, btnEquals;
        private System.Windows.Forms.Button btnAns, btnAS;
        private System.Windows.Forms.Label lblHistory;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.txtDisplay = new System.Windows.Forms.TextBox();
            this.txtHistory = new System.Windows.Forms.RichTextBox();
            this.btnClear = new System.Windows.Forms.Button();
            this.btnClearEntry = new System.Windows.Forms.Button();
            this.btnBackspace = new System.Windows.Forms.Button();
            this.btnDivide = new System.Windows.Forms.Button();
            this.btn7 = new System.Windows.Forms.Button();
            this.btn8 = new System.Windows.Forms.Button();
            this.btn9 = new System.Windows.Forms.Button();
            this.btnMultiply = new System.Windows.Forms.Button();
            this.btn4 = new System.Windows.Forms.Button();
            this.btn5 = new System.Windows.Forms.Button();
            this.btn6 = new System.Windows.Forms.Button();
            this.btnSubtract = new System.Windows.Forms.Button();
            this.btn1 = new System.Windows.Forms.Button();
            this.btn2 = new System.Windows.Forms.Button();
            this.btn3 = new System.Windows.Forms.Button();
            this.btnAdd = new System.Windows.Forms.Button();
            this.btn0 = new System.Windows.Forms.Button();
            this.btnDecimal = new System.Windows.Forms.Button();
            this.btnEquals = new System.Windows.Forms.Button();
            this.btnAns = new System.Windows.Forms.Button();
            this.btnAS = new System.Windows.Forms.Button();
            this.lblHistory = new System.Windows.Forms.Label();
            this.SuspendLayout();
            
            // txtDisplay
            this.txtDisplay.BackColor = System.Drawing.Color.FromArgb(45, 45, 48);
            this.txtDisplay.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtDisplay.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold);
            this.txtDisplay.ForeColor = System.Drawing.Color.White;
            this.txtDisplay.Location = new System.Drawing.Point(20, 20);
            this.txtDisplay.Name = "txtDisplay";
            this.txtDisplay.ReadOnly = true;
            this.txtDisplay.Size = new System.Drawing.Size(360, 43);
            this.txtDisplay.TabIndex = 0;
            this.txtDisplay.Text = "0";
            this.txtDisplay.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            
            // txtHistory
            this.txtHistory.BackColor = System.Drawing.Color.FromArgb(37, 37, 38);
            this.txtHistory.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtHistory.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.txtHistory.ForeColor = System.Drawing.Color.LightGray;
            this.txtHistory.Location = new System.Drawing.Point(400, 45);
            this.txtHistory.Name = "txtHistory";
            this.txtHistory.ReadOnly = true;
            this.txtHistory.Size = new System.Drawing.Size(280, 400);
            this.txtHistory.TabIndex = 1;
            this.txtHistory.Text = "";
            
            // lblHistory
            this.lblHistory.AutoSize = true;
            this.lblHistory.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblHistory.ForeColor = System.Drawing.Color.White;
            this.lblHistory.Location = new System.Drawing.Point(400, 20);
            this.lblHistory.Name = "lblHistory";
            this.lblHistory.Size = new System.Drawing.Size(95, 21);
            this.lblHistory.TabIndex = 25;
            this.lblHistory.Text = "Lịch sử tính";
            
            // Setup buttons with helper method
            SetupButton(this.btnClear, "C", 20, 80, System.Drawing.Color.FromArgb(255, 69, 58), this.btnClear_Click);
            SetupButton(this.btnClearEntry, "CE", 115, 80, System.Drawing.Color.FromArgb(255, 149, 0), this.btnClearEntry_Click);
            SetupButton(this.btnBackspace, "⌫", 210, 80, System.Drawing.Color.FromArgb(255, 149, 0), this.btnBackspace_Click);
            SetupButton(this.btnDivide, "÷", 305, 80, System.Drawing.Color.FromArgb(255, 149, 0), this.btnOperator_Click, new System.Drawing.Size(75, 50));
            
            SetupButton(this.btn7, "7", 20, 140, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btn8, "8", 115, 140, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btn9, "9", 210, 140, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btnMultiply, "×", 305, 140, System.Drawing.Color.FromArgb(255, 149, 0), this.btnOperator_Click, new System.Drawing.Size(75, 50));
            
            SetupButton(this.btn4, "4", 20, 200, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btn5, "5", 115, 200, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btn6, "6", 210, 200, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btnSubtract, "−", 305, 200, System.Drawing.Color.FromArgb(255, 149, 0), this.btnOperator_Click, new System.Drawing.Size(75, 50));
            
            SetupButton(this.btn1, "1", 20, 260, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btn2, "2", 115, 260, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btn3, "3", 210, 260, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btnAdd, "+", 305, 260, System.Drawing.Color.FromArgb(255, 149, 0), this.btnOperator_Click, new System.Drawing.Size(75, 50));
            
            SetupButton(this.btn0, "0", 20, 320, System.Drawing.Color.FromArgb(60, 60, 60), this.btnNumber_Click);
            SetupButton(this.btnDecimal, ".", 115, 320, System.Drawing.Color.FromArgb(60, 60, 60), this.btnDecimal_Click);
            SetupButton(this.btnEquals, "=", 210, 320, System.Drawing.Color.FromArgb(0, 122, 255), this.btnEquals_Click, new System.Drawing.Size(170, 50));
            
            SetupButton(this.btnAns, "ANS", 20, 380, System.Drawing.Color.FromArgb(76, 175, 80), this.btnAns_Click, new System.Drawing.Size(85, 40), 12F);
            SetupButton(this.btnAS, "AS", 115, 380, System.Drawing.Color.FromArgb(156, 39, 176), this.btnAS_Click, new System.Drawing.Size(85, 40), 12F);
            
            // Form setup
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(30, 30, 30);
            this.ClientSize = new System.Drawing.Size(700, 470);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Calculator Pro";
            
            // Add all controls
            this.Controls.Add(this.btnAS);
            this.Controls.Add(this.btnAns);
            this.Controls.Add(this.btnEquals);
            this.Controls.Add(this.btnDecimal);
            this.Controls.Add(this.btn0);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btn3);
            this.Controls.Add(this.btn2);
            this.Controls.Add(this.btn1);
            this.Controls.Add(this.btnSubtract);
            this.Controls.Add(this.btn6);
            this.Controls.Add(this.btn5);
            this.Controls.Add(this.btn4);
            this.Controls.Add(this.btnMultiply);
            this.Controls.Add(this.btn9);
            this.Controls.Add(this.btn8);
            this.Controls.Add(this.btn7);
            this.Controls.Add(this.btnDivide);
            this.Controls.Add(this.btnBackspace);
            this.Controls.Add(this.btnClearEntry);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.lblHistory);
            this.Controls.Add(this.txtHistory);
            this.Controls.Add(this.txtDisplay);
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }
        
        private void SetupButton(System.Windows.Forms.Button btn, string text, int x, int y, System.Drawing.Color color, System.EventHandler clickHandler, System.Drawing.Size? size = null, float fontSize = 18F)
        {
            btn.BackColor = color;
            btn.FlatAppearance.BorderSize = 0;
            btn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            btn.Font = new System.Drawing.Font("Segoe UI", fontSize, System.Drawing.FontStyle.Bold);
            btn.ForeColor = System.Drawing.Color.White;
            btn.Location = new System.Drawing.Point(x, y);
            btn.Size = size ?? new System.Drawing.Size(85, 50);
            btn.Text = text;
            btn.UseVisualStyleBackColor = false;
            btn.Click += clickHandler;
        }
    }
}
