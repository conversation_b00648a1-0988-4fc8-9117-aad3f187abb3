using System;
using System.Collections.Generic;
using System.Globalization;

namespace WindowsFormsApp1
{
    /// <summary>
    /// Engine tính toán cho Calculator với hỗ trợ số thập phân và các phép toán cơ bản
    /// </summary>
    public class CalculatorEngine
    {
        private double _currentValue;
        private double _previousValue;
        private string _currentOperator;
        private bool _operatorPressed;
        private bool _equalsPressed;
        private double _lastAnswer;
        private double _storedValue; // Giá trị AS (Answer Store)

        public CalculatorEngine()
        {
            Reset();
        }

        /// <summary>
        /// Reset calculator về trạng thái ban đầu
        /// </summary>
        public void Reset()
        {
            _currentValue = 0;
            _previousValue = 0;
            _currentOperator = string.Empty;
            _operatorPressed = false;
            _equalsPressed = false;
        }

        /// <summary>
        /// Thêm số vào display
        /// </summary>
        /// <param name="number">Số cần thêm</param>
        /// <param name="currentDisplay"><PERSON><PERSON><PERSON> trị hiện tại trên display</param>
        /// <returns>Giá trị mới cho display</returns>
        public string AddNumber(string number, string currentDisplay)
        {
            if (_operatorPressed || _equalsPressed || currentDisplay == "0")
            {
                _operatorPressed = false;
                _equalsPressed = false;
                return number;
            }
            
            return currentDisplay + number;
        }

        /// <summary>
        /// Thêm dấu thập phân
        /// </summary>
        /// <param name="currentDisplay">Giá trị hiện tại trên display</param>
        /// <returns>Giá trị mới cho display</returns>
        public string AddDecimal(string currentDisplay)
        {
            if (_operatorPressed || _equalsPressed)
            {
                _operatorPressed = false;
                _equalsPressed = false;
                return "0.";
            }

            if (!currentDisplay.Contains("."))
            {
                return currentDisplay + ".";
            }

            return currentDisplay;
        }

        /// <summary>
        /// Xử lý phép toán
        /// </summary>
        /// <param name="operatorSymbol">Ký hiệu phép toán</param>
        /// <param name="currentDisplay">Giá trị hiện tại trên display</param>
        /// <returns>Kết quả tính toán</returns>
        public CalculationResult ProcessOperator(string operatorSymbol, string currentDisplay)
        {
            double displayValue;
            if (!double.TryParse(currentDisplay, NumberStyles.Float, CultureInfo.InvariantCulture, out displayValue))
            {
                return new CalculationResult { Display = "Error", IsError = true };
            }

            if (!string.IsNullOrEmpty(_currentOperator) && !_operatorPressed)
            {
                var result = PerformCalculation(_previousValue, displayValue, _currentOperator);
                if (result.IsError)
                    return result;

                _currentValue = result.Value;
                _previousValue = result.Value;
            }
            else
            {
                _previousValue = displayValue;
                _currentValue = displayValue;
            }

            _currentOperator = operatorSymbol;
            _operatorPressed = true;
            _equalsPressed = false;

            return new CalculationResult 
            { 
                Display = FormatNumber(_currentValue), 
                Value = _currentValue,
                Expression = $"{FormatNumber(_previousValue)} {operatorSymbol}"
            };
        }

        /// <summary>
        /// Tính toán kết quả cuối cùng
        /// </summary>
        /// <param name="currentDisplay">Giá trị hiện tại trên display</param>
        /// <returns>Kết quả tính toán</returns>
        public CalculationResult Calculate(string currentDisplay)
        {
            if (string.IsNullOrEmpty(_currentOperator))
            {
                return new CalculationResult { Display = currentDisplay, Value = _currentValue };
            }

            double displayValue;
            if (!double.TryParse(currentDisplay, NumberStyles.Float, CultureInfo.InvariantCulture, out displayValue))
            {
                return new CalculationResult { Display = "Error", IsError = true };
            }

            var result = PerformCalculation(_previousValue, displayValue, _currentOperator);
            if (result.IsError)
                return result;

            _lastAnswer = result.Value;
            _currentValue = result.Value;
            _equalsPressed = true;

            return new CalculationResult 
            { 
                Display = FormatNumber(result.Value), 
                Value = result.Value,
                Expression = $"{FormatNumber(_previousValue)} {_currentOperator} {FormatNumber(displayValue)} ="
            };
        }

        /// <summary>
        /// Thực hiện phép tính cụ thể
        /// </summary>
        private CalculationResult PerformCalculation(double value1, double value2, string operatorSymbol)
        {
            try
            {
                double result = 0;
                switch (operatorSymbol)
                {
                    case "+":
                        result = value1 + value2;
                        break;
                    case "−":
                        result = value1 - value2;
                        break;
                    case "×":
                        result = value1 * value2;
                        break;
                    case "÷":
                        if (value2 == 0)
                            return new CalculationResult { Display = "Cannot divide by zero", IsError = true };
                        result = value1 / value2;
                        break;
                    default:
                        return new CalculationResult { Display = "Error", IsError = true };
                }

                return new CalculationResult { Display = FormatNumber(result), Value = result };
            }
            catch (OverflowException)
            {
                return new CalculationResult { Display = "Overflow", IsError = true };
            }
            catch (Exception)
            {
                return new CalculationResult { Display = "Error", IsError = true };
            }
        }

        /// <summary>
        /// Format số để hiển thị
        /// </summary>
        private string FormatNumber(double number)
        {
            if (double.IsInfinity(number) || double.IsNaN(number))
                return "Error";

            // Nếu là số nguyên, hiển thị không có phần thập phân
            if (number == Math.Floor(number) && Math.Abs(number) < 1e15)
                return number.ToString("0", CultureInfo.InvariantCulture);

            // Sử dụng định dạng khoa học cho số rất lớn hoặc rất nhỏ
            if (Math.Abs(number) >= 1e15 || (Math.Abs(number) < 1e-6 && number != 0))
                return number.ToString("E6", CultureInfo.InvariantCulture);

            // Định dạng số thập phân bình thường
            return number.ToString("0.##############", CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Xóa ký tự cuối cùng
        /// </summary>
        public string Backspace(string currentDisplay)
        {
            if (_equalsPressed || _operatorPressed)
                return "0";

            if (currentDisplay.Length <= 1)
                return "0";

            return currentDisplay.Substring(0, currentDisplay.Length - 1);
        }

        /// <summary>
        /// Lấy giá trị ANS (kết quả cuối cùng)
        /// </summary>
        public string GetLastAnswer()
        {
            return FormatNumber(_lastAnswer);
        }

        /// <summary>
        /// Lưu giá trị vào AS (Answer Store)
        /// </summary>
        public void StoreValue(string currentDisplay)
        {
            if (double.TryParse(currentDisplay, NumberStyles.Float, CultureInfo.InvariantCulture, out double value))
            {
                _storedValue = value;
            }
        }

        /// <summary>
        /// Lấy giá trị AS đã lưu
        /// </summary>
        public string GetStoredValue()
        {
            return FormatNumber(_storedValue);
        }
    }

    /// <summary>
    /// Kết quả của phép tính toán
    /// </summary>
    public class CalculationResult
    {
        public string Display { get; set; }
        public double Value { get; set; }
        public string Expression { get; set; }
        public bool IsError { get; set; }
    }
}
