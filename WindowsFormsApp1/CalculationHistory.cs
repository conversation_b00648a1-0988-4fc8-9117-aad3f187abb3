using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WindowsFormsApp1
{
    /// <summary>
    /// Quản lý lịch sử tính toán của Calculator
    /// </summary>
    public class CalculationHistory
    {
        private readonly List<HistoryItem> _history;
        private readonly int _maxHistoryItems;

        public CalculationHistory(int maxItems = 50)
        {
            _history = new List<HistoryItem>();
            _maxHistoryItems = maxItems;
        }

        /// <summary>
        /// Thêm một phép tính vào lịch sử
        /// </summary>
        /// <param name="expression"><PERSON><PERSON><PERSON><PERSON> thức tính toán</param>
        /// <param name="result">K<PERSON><PERSON> quả</param>
        public void AddCalculation(string expression, string result)
        {
            if (string.IsNullOrWhiteSpace(expression) || string.IsNullOrWhiteSpace(result))
                return;

            var historyItem = new HistoryItem
            {
                Expression = expression.Trim(),
                Result = result.Trim(),
                Timestamp = DateTime.Now
            };

            _history.Insert(0, historyItem); // Thêm vào đầu danh sách

            // Giới hạn số lượng item trong lịch sử
            if (_history.Count > _maxHistoryItems)
            {
                _history.RemoveAt(_history.Count - 1);
            }
        }

        /// <summary>
        /// Lấy lịch sử dưới dạng text để hiển thị
        /// </summary>
        /// <returns>Chuỗi text chứa lịch sử tính toán</returns>
        public string GetHistoryText()
        {
            if (_history.Count == 0)
                return "Chưa có phép tính nào...";

            var sb = new StringBuilder();
            
            foreach (var item in _history)
            {
                sb.AppendLine($"{item.Expression}");
                sb.AppendLine($"= {item.Result}");
                sb.AppendLine($"[{item.Timestamp:HH:mm:ss}]");
                sb.AppendLine(); // Dòng trống để phân cách
            }

            return sb.ToString();
        }

        /// <summary>
        /// Xóa toàn bộ lịch sử
        /// </summary>
        public void ClearHistory()
        {
            _history.Clear();
        }

        /// <summary>
        /// Lấy kết quả của phép tính gần nhất
        /// </summary>
        /// <returns>Kết quả gần nhất hoặc "0" nếu không có</returns>
        public string GetLastResult()
        {
            if (_history.Count > 0)
                return _history[0].Result;
            
            return "0";
        }

        /// <summary>
        /// Lấy số lượng phép tính trong lịch sử
        /// </summary>
        public int Count => _history.Count;

        /// <summary>
        /// Lấy danh sách các phép tính gần đây
        /// </summary>
        /// <param name="count">Số lượng phép tính cần lấy</param>
        /// <returns>Danh sách các phép tính</returns>
        public List<HistoryItem> GetRecentCalculations(int count = 10)
        {
            return _history.Take(count).ToList();
        }

        /// <summary>
        /// Tìm kiếm trong lịch sử
        /// </summary>
        /// <param name="searchTerm">Từ khóa tìm kiếm</param>
        /// <returns>Danh sách kết quả tìm kiếm</returns>
        public List<HistoryItem> Search(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<HistoryItem>();

            return _history.Where(item => 
                item.Expression.Contains(searchTerm) || 
                item.Result.Contains(searchTerm))
                .ToList();
        }

        /// <summary>
        /// Xuất lịch sử ra định dạng CSV
        /// </summary>
        /// <returns>Chuỗi CSV chứa lịch sử</returns>
        public string ExportToCsv()
        {
            var sb = new StringBuilder();
            sb.AppendLine("Timestamp,Expression,Result");

            foreach (var item in _history.OrderBy(h => h.Timestamp))
            {
                sb.AppendLine($"{item.Timestamp:yyyy-MM-dd HH:mm:ss},\"{item.Expression}\",\"{item.Result}\"");
            }

            return sb.ToString();
        }
    }

    /// <summary>
    /// Đại diện cho một phép tính trong lịch sử
    /// </summary>
    public class HistoryItem
    {
        /// <summary>
        /// Biểu thức tính toán (ví dụ: "5 + 3")
        /// </summary>
        public string Expression { get; set; }

        /// <summary>
        /// Kết quả của phép tính (ví dụ: "8")
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// Thời gian thực hiện phép tính
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Hiển thị thông tin phép tính
        /// </summary>
        /// <returns>Chuỗi mô tả phép tính</returns>
        public override string ToString()
        {
            return $"{Expression} = {Result} [{Timestamp:HH:mm:ss}]";
        }
    }
}
